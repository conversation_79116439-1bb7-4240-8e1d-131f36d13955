name: Python CI

on:
  push:
    branches:
      - main
      - dev
    paths:
      - 'backend/**'
      - 'pyproject.toml'
      - 'uv.lock'
  pull_request:
    branches:
      - main
      - dev
    paths:
      - 'backend/**'
      - 'pyproject.toml'
      - 'uv.lock'

jobs:
  build:
    name: 'Format Backend'
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version:
          - 3.11.x
          - 3.12.x

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '${{ matrix.python-version }}'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black

      - name: Format backend
        run: npm run format:backend

      - name: Check for changes after format
        run: git diff --exit-code
