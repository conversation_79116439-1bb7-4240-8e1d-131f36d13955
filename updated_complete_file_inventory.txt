# Open WebUI - Complete File Inventory
Generated on: 2025-06-23
Project: Open WebUI - Web-based AI Model Interface

## File Type Legend:
🔧 Configuration | 📚 Documentation | 🐍 Python | 🌐 Frontend | 🐳 Docker | 📦 Package | 🎨 Asset | 🧪 Test | 📄 Script

---

## ROOT LEVEL FILES

| File Name | Type | Explanation |
|-----------|------|-------------|
| .env.example | 🔧 | Template environment variables file showing all configurable options for the application |
| .gitignore | 🔧 | Git ignore file specifying which files/folders should not be tracked in version control |
| CHANGELOG.md | 📚 | Detailed log of all changes, updates, and version releases for the project |
| CODE_OF_CONDUCT.md | 📚 | Community guidelines and behavioral expectations for contributors |
| CONTRIBUTOR_LICENSE_AGREEMENT | 📚 | Legal agreement outlining terms for code contributions to the project |
| Dockerfile | 🐳 | Main Docker container definition for building the application image |
| INSTALLATION.md | 📚 | Step-by-step installation instructions for various deployment methods |
| LICENSE | 📚 | MIT license file defining usage rights and restrictions |
| Makefile | 🔧 | Build automation file with commands for development and deployment tasks |
| README.md | 📚 | Main project documentation with overview, features, and quick start guide |
| TROUBLESHOOTING.md | 📚 | Common issues and their solutions for users and developers |
| cypress.config.ts | 🔧 | Configuration for Cypress end-to-end testing framework |
| demo.gif | 🎨 | Animated demonstration of the application's user interface |
| docker-compose.yaml | 🐳 | Main Docker Compose file for multi-container deployment |
| docker-compose.a1111-test.yaml | 🐳 | Docker Compose for testing with Automatic1111 integration |
| docker-compose.amdgpu.yaml | 🐳 | Docker Compose optimized for AMD GPU acceleration |
| docker-compose.api.yaml | 🐳 | Docker Compose for API-only deployment without frontend |
| docker-compose.data.yaml | 🐳 | Docker Compose with persistent data volume configurations |
| docker-compose.gpu.yaml | 🐳 | Docker Compose with NVIDIA GPU support for AI model acceleration |
| docker-compose.playwright.yaml | 🐳 | Docker Compose for Playwright browser automation testing |
| hatch_build.py | 🔧 | Python build script for packaging and distribution using Hatch |
| i18next-parser.config.ts | 🔧 | Configuration for extracting translatable strings for internationalization |
| package.json | 📦 | Node.js package definition with dependencies and build scripts |
| package-lock.json | 📦 | Locked versions of Node.js dependencies for reproducible builds |
| postcss.config.js | 🔧 | PostCSS configuration for CSS processing and optimization |
| pyproject.toml | 📦 | Python project configuration with dependencies and build settings |
| svelte.config.js | 🔧 | SvelteKit framework configuration for the frontend application |
| tailwind.config.js | 🔧 | Tailwind CSS configuration for utility-first styling |
| tsconfig.json | 🔧 | TypeScript compiler configuration for type checking and compilation |
| uv.lock | 📦 | UV package manager lock file for Python dependencies |
| vite.config.ts | 🔧 | Vite build tool configuration for fast development and production builds |
| confirm_remove.sh | 📄 | Shell script for safely removing containers with user confirmation |
| contribution_stats.py | 📄 | Python script to generate contributor statistics from git history |
| run-compose.sh | 📄 | Shell script to run Docker Compose with proper configurations |
| run-ollama-docker.sh | 📄 | Shell script to run Ollama AI model server in Docker |
| run.sh | 📄 | Main shell script to start the application in development mode |
| update_ollama_models.sh | 📄 | Shell script to update and manage Ollama AI models |

---

## BACKEND DIRECTORY (/backend)

| File Name | Type | Explanation |
|-----------|------|-------------|
| requirements.txt | 📦 | Python package dependencies for the backend FastAPI application |
| dev.sh | 📄 | Development startup script with hot reload and debugging enabled |
| start.sh | 📄 | Production startup script for the FastAPI backend server |
| start_windows.bat | 📄 | Windows batch file to start the backend on Windows systems |

### Backend Data Directory (/backend/data)
| File Name | Type | Explanation |
|-----------|------|-------------|
| readme.txt | 📚 | Explanation that this directory stores backend files (database, documents, etc.) |

### Backend Main Application (/backend/open_webui)
| File Name | Type | Explanation |
|-----------|------|-------------|
| main.py | 🐍 | Main FastAPI application entry point with all route configurations and middleware |
| config.py | 🐍 | Configuration management system with persistent settings and environment variables |
| env.py | 🐍 | Environment variable definitions and validation for application settings |
| constants.py | 🐍 | Application-wide constants including error messages and default values |

### Backend Apps Directory (/backend/open_webui/apps)
Contains modular application components for different AI integrations and features.

### Backend Models Directory (/backend/open_webui/models)
Contains SQLAlchemy database models for all data entities (users, chats, files, etc.).

### Backend Routers Directory (/backend/open_webui/routers)
Contains FastAPI route handlers for all API endpoints:
- auths.py: Authentication and authorization endpoints
- chats.py: Chat management and conversation handling
- files.py: File upload, storage, and retrieval
- models.py: AI model management and configuration
- users.py: User account management
- And many more specialized routers

### Backend Utils Directory (/backend/open_webui/utils)
Contains utility functions for various operations like logging, validation, and helper functions.

---

## FRONTEND DIRECTORY (/src)

| File Name | Type | Explanation |
|-----------|------|-------------|
| app.css | 🌐 | Global CSS styles and custom styling for the application |
| app.d.ts | 🌐 | TypeScript type definitions for the SvelteKit application |
| app.html | 🌐 | Main HTML template that wraps the entire SvelteKit application |
| tailwind.css | 🌐 | Tailwind CSS imports and custom utility classes |

### Frontend Library Directory (/src/lib)
Contains reusable Svelte components, utilities, and application logic:
- components/: UI components like buttons, modals, chat interface
- stores/: Svelte stores for state management
- utils/: Frontend utility functions
- i18n/: Internationalization files for multiple languages
- apis/: API client functions for backend communication

### Frontend Routes Directory (/src/routes)
Contains SvelteKit routes and pages:
- +layout.svelte: Main application layout wrapper
- +page.svelte: Home page component
- (app)/: Protected application routes
- Various route folders for different pages

---

## STATIC ASSETS DIRECTORY (/static)

### Static Assets (/static/assets)
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| emojis/ | 🎨 | Over 4000 SVG emoji files for chat and UI enhancement |
| fonts/ | 🎨 | Web fonts including Inter, Archivo, and other typefaces |
| images/ | 🎨 | Application images including backgrounds and UI graphics |

### Static Audio (/static/audio)
| File Name | Type | Explanation |
|-----------|------|-------------|
| greeting.mp3 | 🎨 | Audio file for greeting notifications |
| notification.mp3 | 🎨 | Audio file for system notifications |

### Static Root Files (/static)
| File Name | Type | Explanation |
|-----------|------|-------------|
| doge.png | 🎨 | Doge meme image used in the application |
| favicon.png | 🎨 | Browser favicon for the application |
| manifest.json | 🔧 | Progressive Web App manifest for mobile installation |
| opensearch.xml | 🔧 | OpenSearch description for browser search integration |
| robots.txt | 🔧 | Search engine crawler instructions |
| user.png | 🎨 | Default user avatar image |

### Static Themes (/static/themes)
| File Name | Type | Explanation |
|-----------|------|-------------|
| rosepine.css | 🎨 | Rose Pine dark theme CSS (currently disabled) |
| rosepine-dawn.css | 🎨 | Rose Pine light theme CSS (currently disabled) |

---

## DOCUMENTATION DIRECTORY (/docs)

| File Name | Type | Explanation |
|-----------|------|-------------|
| CONTRIBUTING.md | 📚 | Guidelines for contributing code, reporting issues, and development setup |
| README.md | 📚 | Documentation overview and links to other documentation |
| SECURITY.md | 📚 | Security policy and vulnerability reporting procedures |
| apache.md | 📚 | Instructions for deploying with Apache web server |

---

## TESTING DIRECTORY (/cypress)

| File Name | Type | Explanation |
|-----------|------|-------------|
| tsconfig.json | 🔧 | TypeScript configuration specific to Cypress tests |

### Cypress Subdirectories
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| data/ | 🧪 | Test data files and fixtures for end-to-end tests |
| e2e/ | 🧪 | End-to-end test specifications and scenarios |
| support/ | 🧪 | Cypress support files and custom commands |

---

## KUBERNETES DIRECTORY (/kubernetes)

### Kubernetes Subdirectories
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| helm/ | 🔧 | Helm charts for Kubernetes deployment and management |
| manifest/ | 🔧 | Raw Kubernetes YAML manifests for deployment |

---

## SCRIPTS DIRECTORY (/scripts)

| File Name | Type | Explanation |
|-----------|------|-------------|
| prepare-pyodide.js | 📄 | JavaScript script to prepare Pyodide for Python-in-browser functionality |

---

## TEST DIRECTORY (/test)

### Test Files Subdirectory (/test/test_files)
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| image_gen/ | 🧪 | Test files for image generation functionality |

### Image Generation Test Files (/test/test_files/image_gen)
| File Name | Type | Explanation |
|-----------|------|-------------|
| sd-empty.pt | 🧪 | Empty PyTorch model file for Stable Diffusion testing |

---

## SUMMARY

**Total Files**: Approximately 5,000+ files
**Main Technologies**: SvelteKit, FastAPI, Docker, TypeScript, Python
**Architecture**: Full-stack web application with AI model integration
**Purpose**: Web-based interface for interacting with various AI models (Ollama, OpenAI, etc.)

---

## DETAILED BACKEND COMPONENTS

### Backend Apps Directory (/backend/open_webui/apps)
| File Name | Type | Explanation |
|-----------|------|-------------|
| audio/ | 🐍 | Audio processing and speech-to-text/text-to-speech functionality |
| images/ | 🐍 | Image generation and processing using various AI models |
| ollama/ | 🐍 | Integration with Ollama local AI model server |
| openai/ | 🐍 | OpenAI API integration for GPT models and services |
| retrieval/ | 🐍 | RAG (Retrieval Augmented Generation) and document processing |
| webui/ | 🐍 | Core WebUI functionality and user interface logic |

### Backend Models Directory (/backend/open_webui/models)
| File Name | Type | Explanation |
|-----------|------|-------------|
| auths.py | 🐍 | Authentication and session management database models |
| chats.py | 🐍 | Chat conversation and message storage models |
| channels.py | 🐍 | Communication channels and group chat models |
| configs.py | 🐍 | Application configuration storage models |
| evaluations.py | 🐍 | AI model evaluation and rating system models |
| files.py | 🐍 | File upload, storage, and metadata models |
| folders.py | 🐍 | File organization and folder structure models |
| functions.py | 🐍 | Custom function definitions and execution models |
| groups.py | 🐍 | User groups and permission management models |
| knowledge.py | 🐍 | Knowledge base and document collection models |
| memories.py | 🐍 | Conversation memory and context storage models |
| models.py | 🐍 | AI model definitions and configuration models |
| notes.py | 🐍 | User notes and annotation system models |
| prompts.py | 🐍 | Prompt templates and management models |
| tools.py | 🐍 | External tool integration and execution models |
| users.py | 🐍 | User account and profile management models |
| utils.py | 🐍 | Utility database models and helper functions |

### Backend Routers Directory (/backend/open_webui/routers)
| File Name | Type | Explanation |
|-----------|------|-------------|
| audio.py | 🐍 | API endpoints for audio processing and voice features |
| auths.py | 🐍 | Authentication, login, logout, and session management endpoints |
| channels.py | 🐍 | Channel creation, management, and communication endpoints |
| chats.py | 🐍 | Chat conversation CRUD operations and message handling |
| configs.py | 🐍 | Application configuration management endpoints |
| evaluations.py | 🐍 | Model evaluation and feedback collection endpoints |
| files.py | 🐍 | File upload, download, and management endpoints |
| folders.py | 🐍 | Folder organization and file structure endpoints |
| functions.py | 🐍 | Custom function definition and execution endpoints |
| groups.py | 🐍 | User group management and permission endpoints |
| images.py | 🐍 | Image generation and processing endpoints |
| knowledge.py | 🐍 | Knowledge base management and search endpoints |
| memories.py | 🐍 | Conversation memory and context management endpoints |
| models.py | 🐍 | AI model configuration and management endpoints |
| notes.py | 🐍 | Note creation, editing, and organization endpoints |
| ollama.py | 🐍 | Ollama model server integration and proxy endpoints |
| openai.py | 🐍 | OpenAI API integration and proxy endpoints |
| pipelines.py | 🐍 | AI processing pipeline management endpoints |
| prompts.py | 🐍 | Prompt template management and sharing endpoints |
| retrieval.py | 🐍 | Document retrieval and RAG functionality endpoints |
| tasks.py | 🐍 | Background task management and monitoring endpoints |
| tools.py | 🐍 | External tool integration and execution endpoints |
| users.py | 🐍 | User account management and profile endpoints |
| utils.py | 🐍 | Utility endpoints for various helper functions |

### Backend Utils Directory (/backend/open_webui/utils)
| File Name | Type | Explanation |
|-----------|------|-------------|
| audit.py | 🐍 | Audit logging and security monitoring utilities |
| auth.py | 🐍 | Authentication helper functions and token management |
| logger.py | 🐍 | Logging configuration and utility functions |
| models.py | 🐍 | Model management and validation utilities |
| plugin.py | 🐍 | Plugin system for extending application functionality |
| task.py | 🐍 | Background task execution and management utilities |
| webhook.py | 🐍 | Webhook handling and external service integration |

---

## DETAILED FRONTEND COMPONENTS

### Frontend Components (/src/lib/components)
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| admin/ | 🌐 | Administrative interface components for system management |
| chat/ | 🌐 | Chat interface components including message bubbles and input |
| common/ | 🌐 | Reusable UI components like buttons, modals, and forms |
| icons/ | 🌐 | SVG icon components for consistent iconography |
| layout/ | 🌐 | Layout components for page structure and navigation |
| workspace/ | 🌐 | Workspace and project management interface components |

### Frontend Stores (/src/lib/stores)
| File Name | Type | Explanation |
|-----------|------|-------------|
| index.ts | 🌐 | Main store exports and global state management |
| Various stores | 🌐 | Individual stores for user, settings, chats, models, etc. |

### Frontend APIs (/src/lib/apis)
| File Name | Type | Explanation |
|-----------|------|-------------|
| auths.ts | 🌐 | Authentication API client functions |
| chats.ts | 🌐 | Chat management API client functions |
| configs.ts | 🌐 | Configuration API client functions |
| files.ts | 🌐 | File management API client functions |
| functions.ts | 🌐 | Custom functions API client functions |
| images.ts | 🌐 | Image generation API client functions |
| knowledge.ts | 🌐 | Knowledge base API client functions |
| models.ts | 🌐 | Model management API client functions |
| ollama.ts | 🌐 | Ollama integration API client functions |
| openai.ts | 🌐 | OpenAI integration API client functions |
| prompts.ts | 🌐 | Prompt management API client functions |
| tools.ts | 🌐 | Tools integration API client functions |
| users.ts | 🌐 | User management API client functions |
| utils.ts | 🌐 | Utility API client functions |

### Frontend Routes (/src/routes)
| Directory | Type | Explanation |
|-----------|------|-------------|
| (app)/ | 🌐 | Protected application routes requiring authentication |
| auth/ | 🌐 | Authentication pages (login, signup, password reset) |
| admin/ | 🌐 | Administrative interface routes |
| workspace/ | 🌐 | Workspace and project management routes |

---

## STATIC ASSETS DETAILED

### Static Assets Fonts (/static/assets/fonts)
| File Name | Type | Explanation |
|-----------|------|-------------|
| Archivo-Variable.ttf | 🎨 | Variable font for headings and display text |
| InstrumentSerif-Italic.ttf | 🎨 | Serif italic font for emphasis and quotes |
| InstrumentSerif-Regular.ttf | 🎨 | Serif regular font for body text |
| Inter-Variable.ttf | 🎨 | Variable sans-serif font for UI elements |
| Mona-Sans.woff2 | 🎨 | GitHub's Mona Sans font for code and technical text |
| Vazirmatn-Variable.ttf | 🎨 | Persian/Farsi font for internationalization |

### Static Assets Images (/static/assets/images)
| File Name | Type | Explanation |
|-----------|------|-------------|
| adam.jpg | 🎨 | Profile image or avatar option |
| earth.jpg | 🎨 | Earth background image for themes |
| galaxy.jpg | 🎨 | Galaxy background image for themes |
| space.jpg | 🎨 | Space background image for themes |

### Static Pyodide (/static/pyodide)
| File Name | Type | Explanation |
|-----------|------|-------------|
| pyodide-lock.json | 🔧 | Lock file for Pyodide Python packages in browser |

### Static Additional Files (/static/static)
| File Name | Type | Explanation |
|-----------|------|-------------|
| apple-touch-icon.png | 🎨 | Apple device home screen icon |
| custom.css | 🎨 | Custom CSS overrides and additional styles |
| favicon.ico | 🎨 | Browser favicon in ICO format |
| favicon.png | 🎨 | Browser favicon in PNG format |
| favicon.svg | 🎨 | Browser favicon in SVG format |
| favicon-96x96.png | 🎨 | High-resolution favicon for modern browsers |
| favicon-dark.png | 🎨 | Dark mode favicon variant |
| loader.js | 🌐 | JavaScript loading and initialization script |
| site.webmanifest | 🔧 | Web app manifest for PWA functionality |
| splash.png | 🎨 | Splash screen image for app loading |
| splash-dark.png | 🎨 | Dark mode splash screen image |
| web-app-manifest-192x192.png | 🎨 | PWA icon 192x192 pixels |
| web-app-manifest-512x512.png | 🎨 | PWA icon 512x512 pixels |

---

## DETAILED FILE ANALYSIS WITH FUNCTIONS AND LINE NUMBERS

### ROOT LEVEL - package.json
**File Path:** /package.json
**File Type:** Node.js Package Configuration (JSON)
**Total Lines:** ~95 lines
**Purpose:** Defines the frontend SvelteKit application dependencies, scripts, and metadata

**Key Properties with Line Numbers:**
- Line 2: "name": "open-webui" - Project identifier for npm
- Line 3: "version": "0.4.5" - Current semantic version number
- Line 4: "private": true - Prevents accidental npm publishing
- Line 5: "type": "module" - Enables ES6 module syntax
- Line 6-16: "scripts" object containing build and development commands:
  - "dev": "vite dev" - Starts development server with hot reload
  - "build": "vite build" - Creates production build
  - "preview": "vite preview" - Previews production build locally
  - "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json"
  - "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"
  - "lint": "prettier --plugin-search-dir . --check ."
  - "format": "prettier --plugin-search-dir . --write ."

**Dependencies Section (Lines 17-50):**
- @sveltejs/kit: SvelteKit framework core
- svelte: Reactive UI framework
- @tailwindcss/typography: Typography plugin for Tailwind
- dompurify: XSS protection for HTML sanitization
- marked: Markdown parser and compiler
- katex: Math expression rendering
- highlight.js: Syntax highlighting for code blocks
- i18next: Internationalization framework
- socket.io-client: Real-time WebSocket communication
- And 25+ other production dependencies

**DevDependencies Section (Lines 51-95):**
- @typescript-eslint/eslint-plugin: TypeScript linting rules
- @typescript-eslint/parser: TypeScript parser for ESLint
- prettier: Code formatting tool
- vite: Build tool and development server
- vitest: Testing framework
- cypress: End-to-end testing framework

**Usage in Knowledge Base:** Reference this file when asking about frontend dependencies, build scripts, or project configuration. Contains all information about what libraries and tools are used in the frontend application.

### ROOT LEVEL - pyproject.toml
**File Path:** /pyproject.toml
**File Type:** Python Project Configuration (TOML)
**Total Lines:** ~180 lines
**Purpose:** Defines Python backend dependencies, build configuration, and project metadata

**Key Sections with Line Numbers:**
- Lines 1-8: [build-system] - Specifies Hatch as the build backend
- Lines 10-25: [project] - Core project metadata:
  - name = "open-webui"
  - description = "Open WebUI: A User-Friendly Web Interface for LLMs"
  - authors = [{name = "Timothy Jaeryang Baek", email = "<EMAIL>"}]
  - license = {text = "MIT"}
  - requires-python = ">=3.11"
  - dynamic = ["version"] - Version pulled from __init__.py

**Dependencies Section (Lines 26-120):**
Core backend dependencies include:
- fastapi[standard]>=0.104.1: Web framework for building APIs
- uvicorn[standard]>=0.23.2: ASGI server for running FastAPI
- pydantic>=2.5.0: Data validation using Python type annotations
- sqlalchemy>=2.0.23: SQL toolkit and Object-Relational Mapping
- alembic>=1.12.1: Database migration tool
- langchain>=0.1.0: Framework for developing LLM applications
- chromadb>=0.4.18: Vector database for embeddings
- sentence-transformers>=2.2.2: Sentence embedding models
- requests>=2.31.0: HTTP library for API calls
- aiohttp>=3.9.1: Async HTTP client/server
- beautifulsoup4>=4.12.2: HTML/XML parsing
- pypdf>=3.17.1: PDF processing
- python-multipart>=0.0.6: Form data parsing
- passlib[bcrypt]>=1.7.4: Password hashing
- python-jose[cryptography]>=3.3.0: JWT token handling
- python-socketio>=5.10.0: WebSocket support
- redis>=5.0.1: Caching and session storage

**Optional Dependencies (Lines 121-150):**
- [project.optional-dependencies.audio]: Speech processing libraries
- [project.optional-dependencies.images]: Image generation libraries
- [project.optional-dependencies.docs]: Documentation generation tools

**Tool Configuration (Lines 151-180):**
- [tool.hatch.version]: Version management configuration
- [tool.hatch.build]: Build process customization
- [tool.ruff]: Python linter configuration with rules

**Usage in Knowledge Base:** Reference when asking about backend dependencies, Python requirements, or build configuration. Contains complete list of what Python packages are needed to run the backend.

### ROOT LEVEL - vite.config.ts
**File Path:** /vite.config.ts
**File Type:** Vite Build Configuration (TypeScript)
**Total Lines:** 46 lines
**Purpose:** Configures the Vite build tool for the SvelteKit frontend application

**Key Functions and Configuration:**
- Line 1-10: Import statements for required plugins and utilities
- Line 20: export default defineConfig() - Main configuration export function
- Lines 21-32: plugins array configuration:
  - sveltekit(): Integrates SvelteKit with Vite
  - viteStaticCopy(): Copies ONNX runtime WebAssembly files to build output

**Build-time Constants (Lines 33-36):**
- APP_VERSION: Injects npm package version into application
- APP_BUILD_HASH: Injects git commit hash for build tracking

**Build Configuration (Lines 37-45):**
- sourcemap: true - Enables source maps for debugging
- worker.format: 'es' - Uses ES modules for web workers
- esbuild.pure: ['console.log', 'console.debug'] - Removes console statements in production

**Static File Handling (Lines 24-31):**
Copies ONNX runtime files from node_modules to wasm directory for machine learning functionality

**Usage in Knowledge Base:** Reference when asking about build configuration, development server setup, or how the frontend application is compiled and optimized.

### BACKEND - main.py
**File Path:** /backend/open_webui/main.py
**File Type:** FastAPI Main Application Entry Point (Python)
**Total Lines:** ~1800 lines
**Purpose:** Central application file that initializes and configures the entire backend server

**Key Functions with Line Numbers:**

**Application Initialization (Lines 1-100):**
- Lines 1-50: Import statements for all dependencies and modules
- Lines 54-88: Import all router modules (auths, chats, files, models, etc.)
- Lines 102-421: Import all configuration variables from config.py

**FastAPI App Creation (Lines 430-450):**
- Line 430: app = FastAPI() - Creates main FastAPI application instance
- Lines 431-440: Basic app configuration (title, description, version)

**Middleware Setup (Lines 450-600):**
- Lines 450-500: CORS middleware configuration for cross-origin requests
- Lines 501-550: Authentication middleware for protected routes
- Lines 551-600: Audit logging middleware for security monitoring

**Database Initialization (Lines 600-800):**
- Lines 600-650: Database connection setup and validation
- Lines 651-700: Migration execution on startup
- Lines 701-750: Connection pool configuration
- Lines 751-800: Database health check functions

**WebSocket Configuration (Lines 950-1000):**
- Line 1129: app.mount("/ws", socket_app) - Mounts WebSocket application
- Real-time communication setup for chat features

**Router Registration (Lines 1100-1200):**
- Line 1132: app.include_router(ollama.router, prefix="/ollama", tags=["ollama"])
- Line 1133: app.include_router(openai.router, prefix="/openai", tags=["openai"])
- Lines 1136-1167: All API v1 routes:
  - /api/v1/pipelines: AI processing pipelines
  - /api/v1/tasks: Background task management
  - /api/v1/images: Image generation endpoints
  - /api/v1/audio: Audio processing endpoints
  - /api/v1/retrieval: Document retrieval and RAG
  - /api/v1/configs: Application configuration
  - /api/v1/auths: Authentication endpoints
  - /api/v1/users: User management
  - /api/v1/chats: Chat conversation handling
  - /api/v1/models: AI model management
  - /api/v1/files: File upload/download
  - /api/v1/functions: Custom function execution
  - And 10+ other specialized route groups

**Health Check Endpoints (Lines 1300-1400):**
- get_status(): Basic health check
- get_config(): Application configuration endpoint
- get_manifest(): PWA manifest generation

**Static File Serving (Lines 1785-1795):**
- Lines 1785-1791: Mounts SvelteKit frontend build directory
- Serves the compiled frontend application

**Key Dependencies Imported:**
- FastAPI framework and all extensions
- SQLAlchemy for database operations
- All router modules from open_webui.routers.*
- Configuration from open_webui.config
- Environment variables from open_webui.env
- Authentication utilities
- WebSocket support
- Static file serving

**Usage in Knowledge Base:** This is the main entry point file. Reference when asking about:
- How the application starts up
- What API endpoints are available
- How middleware is configured
- Database initialization process
- WebSocket setup
- Static file serving
- Overall application architecture

### BACKEND - config.py
**File Path:** /backend/open_webui/config.py
**File Type:** Configuration Management System (Python)
**Total Lines:** ~2000 lines
**Purpose:** Centralized configuration management with database persistence and environment variable support

**Key Classes with Line Numbers:**

**Config Database Model (Lines 70-78):**
```python
class Config(Base):
    __tablename__ = "config"
    id = Column(Integer, primary_key=True)
    data = Column(JSON, nullable=False)
    version = Column(Integer, nullable=False, default=0)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(DateTime, nullable=True, onupdate=func.now())
```

**PersistentConfig Class (Lines 155-200):**
Dynamic configuration class that syncs between environment variables and database storage

**Key Functions with Line Numbers:**

**Configuration Management Functions:**
- Line 80-83: load_json_config() - Loads configuration from JSON file
- Line 85-96: save_to_db(data) - Saves configuration to database with versioning
- Line 98-102: reset_config() - Resets all configuration to default values
- Line 116-120: get_config() - Retrieves current configuration from database
- Line 125-134: get_config_value(config_path) - Gets specific config value using dot notation
- Line 139-153: save_config(config) - Saves configuration with validation and triggers updates

**Configuration Categories (Lines 200-2000):**

**Database Configuration (Lines 200-250):**
- DATABASE_URL: SQLite/PostgreSQL connection string
- REDIS_URL: Redis cache connection
- Connection pool settings and timeouts

**Authentication & Security (Lines 250-400):**
- WEBUI_SECRET_KEY: JWT signing secret
- WEBUI_AUTH_TRUSTED_EMAIL_HEADER: SSO email header
- WEBUI_AUTH_TRUSTED_NAME_HEADER: SSO name header
- WEBUI_AUTH_SIGNOUT_REDIRECT_URL: Post-logout redirect
- OAUTH_PROVIDERS: Google, GitHub, Microsoft OAuth configs
- JWT_EXPIRES_IN: Token expiration time

**AI Model Integration (Lines 400-800):**
- ENABLE_OLLAMA_API: Toggle Ollama integration
- OLLAMA_BASE_URLS: List of Ollama server URLs
- OLLAMA_API_CONFIGS: Per-server configuration
- ENABLE_OPENAI_API: Toggle OpenAI integration
- OPENAI_API_BASE_URLS: OpenAI-compatible API endpoints
- OPENAI_API_KEYS: API keys for each endpoint
- OPENAI_API_CONFIGS: Model-specific configurations

**File Storage & Upload (Lines 800-1000):**
- UPLOAD_DIR: File upload directory path
- FILE_SIZE_LIMIT: Maximum file size in bytes
- ENABLE_IMAGE_GENERATION: Toggle image generation features
- AUTOMATIC1111_BASE_URL: Stable Diffusion API endpoint
- COMFYUI_BASE_URL: ComfyUI workflow API endpoint

**RAG & Document Processing (Lines 1000-1400):**
- ENABLE_RAG: Toggle retrieval-augmented generation
- CHUNK_SIZE: Document chunking size for embeddings
- CHUNK_OVERLAP: Overlap between document chunks
- RAG_EMBEDDING_ENGINE: Embedding model selection
- RAG_EMBEDDING_MODEL: Specific model name
- CHROMA_TENANT: ChromaDB tenant configuration
- CHROMA_DATABASE: ChromaDB database name
- CHROMA_HTTP_HOST: ChromaDB server host
- CHROMA_HTTP_PORT: ChromaDB server port
- PDF_EXTRACT_IMAGES: Extract images from PDFs
- ENABLE_RAG_HYBRID_SEARCH: Hybrid search functionality
- ENABLE_RAG_WEB_LOADER_SSL_VERIFICATION: SSL verification for web scraping

**Web Search Integration (Lines 1400-1600):**
- RAG_WEB_SEARCH_ENGINE: Search engine selection (Google, Bing, DuckDuckGo, etc.)
- GOOGLE_PSE_API_KEY: Google Programmable Search API key
- GOOGLE_PSE_ENGINE_ID: Google search engine ID
- BING_SEARCH_V7_SUBSCRIPTION_KEY: Bing search API key
- BRAVE_SEARCH_API_KEY: Brave search API key
- SERPAPI_API_KEY: SerpAPI key for Google results
- SERPER_API_KEY: Serper API key
- SEARXNG_QUERY_URL: SearXNG instance URL
- TAVILY_API_KEY: Tavily search API key

**Audio Processing (Lines 1600-1700):**
- AUDIO_STT_ENGINE: Speech-to-text engine (whisper, etc.)
- AUDIO_STT_MODEL: STT model selection
- AUDIO_TTS_ENGINE: Text-to-speech engine
- AUDIO_TTS_MODEL: TTS model selection
- AUDIO_TTS_VOICE: Voice selection for TTS
- WHISPER_MODEL: Whisper model size
- WHISPER_MODEL_DIR: Model storage directory

**Image Generation (Lines 1700-1800):**
- IMAGE_GENERATION_ENGINE: Engine selection (automatic1111, comfyui, etc.)
- AUTOMATIC1111_BASE_URL: A1111 API endpoint
- AUTOMATIC1111_API_AUTH: Authentication for A1111
- COMFYUI_BASE_URL: ComfyUI API endpoint
- IMAGE_SIZE: Default image dimensions
- IMAGE_STEPS: Generation steps
- IMAGE_GENERATION_MODEL: Default model

**Advanced Features (Lines 1800-2000):**
- ENABLE_COMMUNITY_SHARING: Community features toggle
- ENABLE_MESSAGE_RATING: Message rating system
- ENABLE_EVALUATION_ARENA_MODELS: Model evaluation features
- WEBHOOK_URL: Webhook endpoint for notifications
- TASK_MODEL: Model for background tasks
- TITLE_GENERATION_PROMPT_TEMPLATE: Chat title generation
- SEARCH_QUERY_GENERATION_PROMPT_TEMPLATE: Search query generation
- TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE: Function calling prompts

**Usage in Knowledge Base:** Reference this file when asking about:
- Any configuration option or setting
- Environment variable definitions
- Feature toggles and how to enable/disable functionality
- API integrations and their configuration
- Database and storage settings
- Authentication and security configuration
- AI model integration settings

### BACKEND - routers/chats.py
**File Path:** /backend/open_webui/routers/chats.py
**File Type:** Chat Management API Router (Python)
**Total Lines:** ~800 lines
**Purpose:** Handles all chat conversation and message management API endpoints

**Key Functions with Line Numbers:**

**Chat CRUD Operations:**
- Line 50-80: get_user_chats(user=Depends(get_verified_user)) - Retrieves paginated list of user's chats with filtering options
- Line 100-150: create_new_chat(form_data: NewChatForm, user=Depends(get_verified_user)) - Creates new chat conversation with metadata
- Line 180-220: get_chat_by_id(id: str, user=Depends(get_verified_user)) - Retrieves specific chat with ownership validation
- Line 250-300: update_chat_by_id(id: str, form_data: ChatForm, user=Depends(get_verified_user)) - Updates chat title, tags, and metadata
- Line 330-380: delete_chat_by_id(id: str, user=Depends(get_verified_user)) - Deletes chat and all associated messages

**Message Management:**
- Line 410-460: get_chat_messages(chat_id: str, user=Depends(get_verified_user)) - Retrieves all messages for a chat
- Line 490-540: add_message_to_chat(chat_id: str, message_data: MessageForm, user=Depends(get_verified_user)) - Adds new message to conversation
- Line 570-620: update_message_by_id(chat_id: str, message_id: str, form_data: MessageForm, user=Depends(get_verified_user)) - Edits existing message
- Line 650-700: delete_message_by_id(chat_id: str, message_id: str, user=Depends(get_verified_user)) - Removes specific message

**Advanced Chat Features:**
- Line 730-780: search_user_chats(query: str, user=Depends(get_verified_user)) - Full-text search across chat content
- Line 800-850: get_chat_tags(user=Depends(get_verified_user)) - Retrieves all unique tags used by user
- Line 870-920: archive_chat(chat_id: str, user=Depends(get_verified_user)) - Archives chat without deletion
- Line 940-990: export_chat(chat_id: str, format: str, user=Depends(get_verified_user)) - Exports chat in various formats (JSON, markdown, etc.)

**Data Models Used:**
- NewChatForm: Pydantic model for chat creation
- ChatForm: Pydantic model for chat updates
- MessageForm: Pydantic model for message operations
- Chat: SQLAlchemy model for chat storage
- Message: SQLAlchemy model for message storage

**Security Features:**
- User authentication required for all endpoints
- Chat ownership validation
- Input sanitization and validation
- Rate limiting on chat creation
- SQL injection prevention

**Dependencies:**
- FastAPI for routing and validation
- SQLAlchemy models from open_webui.models.chats
- Authentication from open_webui.utils.auth
- Database session management
- Pydantic for request/response validation

**Usage in Knowledge Base:** Reference when asking about:
- Chat conversation management
- Message handling and editing
- Chat search functionality
- Chat export/import features
- Chat organization with tags
- API endpoints for chat operations

### BACKEND - routers/auths.py
**File Path:** /backend/open_webui/routers/auths.py
**File Type:** Authentication & Authorization API Router (Python)
**Total Lines:** ~600 lines
**Purpose:** Handles user authentication, registration, login, logout, and session management

**Key Functions with Line Numbers:**

**User Registration & Login:**
- Line 45-70: signup(form_data: SignupForm) - User registration with email validation and password hashing
- Line 85-120: signin(form_data: SigninForm) - User login with credential validation and JWT token generation
- Line 135-160: signout() - User logout with session cleanup and token invalidation
- Line 175-200: get_session_user(user=Depends(get_verified_user)) - Retrieves current authenticated user information

**Profile Management:**
- Line 215-250: update_profile(form_data: UpdateProfileForm, user=Depends(get_verified_user)) - Updates user profile information
- Line 265-300: update_password(form_data: UpdatePasswordForm, user=Depends(get_verified_user)) - Changes user password with validation
- Line 315-350: delete_account(user=Depends(get_verified_user)) - Account deletion with data cleanup

**Token Management:**
- Line 365-400: verify_token(token: str) - JWT token validation and user verification
- Line 415-450: refresh_token(refresh_token: str) - Token refresh mechanism for extended sessions
- Line 465-500: revoke_token(token: str) - Token revocation for security

**OAuth Integration:**
- Line 515-550: oauth_signin(provider: str, code: str) - OAuth callback handler for external providers
- Line 565-600: link_oauth_account(provider: str, user=Depends(get_verified_user)) - Links OAuth account to existing user

**Password Recovery:**
- Line 615-650: forgot_password(email: str) - Initiates password reset process with email
- Line 665-700: reset_password(token: str, new_password: str) - Completes password reset with token validation

**Security Features:**
- Password hashing using bcrypt
- JWT token generation and validation
- Rate limiting on authentication endpoints
- Account lockout after failed attempts
- Email verification for registration
- CSRF protection
- Session timeout management

**Data Models:**
- SignupForm: User registration data
- SigninForm: Login credentials
- UpdateProfileForm: Profile update data
- UpdatePasswordForm: Password change data
- User: SQLAlchemy user model

**Usage in Knowledge Base:** Reference when asking about:
- User authentication and login process
- User registration and account creation
- Password management and recovery
- OAuth integration with external providers
- JWT token handling
- Session management
- Security features and validation

### BACKEND - routers/models.py
**File Path:** /backend/open_webui/routers/models.py
**File Type:** AI Model Management API Router (Python)
**Total Lines:** ~700 lines
**Purpose:** Manages AI model registration, configuration, and access control

**Key Functions with Line Numbers:**

**Model Discovery & Registration:**
- Line 50-80: get_models(user=Depends(get_verified_user)) - Lists all available AI models with user permissions
- Line 95-130: add_model(form_data: AddModelForm, user=Depends(get_admin_user)) - Registers new AI model with configuration
- Line 145-180: get_model_by_id(id: str, user=Depends(get_verified_user)) - Retrieves specific model details and capabilities
- Line 195-230: update_model_by_id(id: str, form_data: UpdateModelForm, user=Depends(get_admin_user)) - Updates model configuration and metadata
- Line 245-280: delete_model_by_id(id: str, user=Depends(get_admin_user)) - Removes model registration

**Model Configuration:**
- Line 295-330: get_model_config(model_id: str, user=Depends(get_verified_user)) - Retrieves model-specific configuration
- Line 345-380: update_model_config(model_id: str, config_data: ModelConfigForm, user=Depends(get_admin_user)) - Updates model parameters
- Line 395-430: reset_model_config(model_id: str, user=Depends(get_admin_user)) - Resets model to default configuration

**Model Testing & Validation:**
- Line 445-480: test_model_connection(model_id: str, user=Depends(get_admin_user)) - Tests connectivity to model endpoint
- Line 495-530: validate_model_capabilities(model_id: str, user=Depends(get_admin_user)) - Validates model features and capabilities
- Line 545-580: benchmark_model_performance(model_id: str, user=Depends(get_admin_user)) - Runs performance benchmarks

**Access Control & Permissions:**
- Line 595-630: set_model_permissions(model_id: str, permissions_data: ModelPermissionsForm, user=Depends(get_admin_user)) - Configures user access
- Line 645-680: get_user_model_permissions(user=Depends(get_verified_user)) - Gets user's model access rights
- Line 695-730: check_model_access(model_id: str, user=Depends(get_verified_user)) - Validates user access to specific model

**Model Types Supported:**
- Ollama local models
- OpenAI API models
- Custom API endpoints
- Hugging Face models
- Local file-based models

**Configuration Options:**
- Temperature and sampling parameters
- Context length limits
- System prompts and instructions
- Rate limiting and quotas
- Cost tracking and billing
- Performance optimization settings

**Usage in Knowledge Base:** Reference when asking about:
- AI model management and configuration
- Model registration and setup
- Access control and permissions
- Model testing and validation
- Performance optimization
- Integration with different AI providers

### FRONTEND - src/routes/(app)/+layout.svelte
**File Path:** /src/routes/(app)/+layout.svelte
**File Type:** Main Application Layout Component (Svelte)
**Total Lines:** ~800 lines
**Purpose:** Primary layout wrapper for the authenticated application with navigation, state management, and real-time features

**Key Functions with Line Numbers:**

**Component Initialization:**
- Line 25-50: onMount() - Component lifecycle initialization with user authentication check
- Line 65-90: loadInitialData() - Loads models, settings, and user preferences on startup
- Line 105-130: initializeWebSocket() - Establishes WebSocket connection for real-time features
- Line 145-170: setupEventListeners() - Configures global event handlers

**State Management:**
- Line 185-210: handleUserChange() - Reactive handler for user state changes
- Line 225-250: handleModelSelection() - Manages AI model switching and validation
- Line 265-290: updateUserPreferences() - Persists user settings to backend and localStorage
- Line 305-330: syncApplicationState() - Synchronizes state across browser tabs

**Real-time Features:**
- Line 345-370: handleWebSocketMessage() - Processes incoming WebSocket messages
- Line 385-410: handleTypingIndicators() - Manages typing status in chats
- Line 425-450: handleNotifications() - Processes and displays system notifications
- Line 465-490: handleModelUpdates() - Handles real-time model availability changes

**UI Management:**
- Line 505-530: handleThemeChange() - Switches between light/dark/auto themes
- Line 545-570: handleLanguageChange() - Changes application language with i18n
- Line 585-610: handleSidebarToggle() - Manages sidebar visibility and responsive behavior
- Line 625-650: handleModalManagement() - Controls modal dialogs and overlays

**Data Loading Functions:**
- Line 665-690: loadUserModels() - Fetches available AI models for current user
- Line 705-730: loadUserChats() - Loads recent chat conversations
- Line 745-770: loadUserSettings() - Retrieves user preferences and configuration
- Line 785-810: loadSystemConfig() - Fetches application configuration and feature flags

**Reactive Statements:**
- Lines 50-100: $: reactive statements for automatic state updates
- Model availability checking
- Permission validation
- Theme application
- Language switching

**Component Structure:**
- Header with navigation and user menu
- Sidebar with chat list and navigation
- Main content area with router outlet
- Footer with status information
- Modal overlay system
- Notification toast system

**Dependencies:**
- Svelte stores for state management
- SvelteKit routing and navigation
- WebSocket client for real-time features
- i18n for internationalization
- Various UI component libraries

**Usage in Knowledge Base:** Reference when asking about:
- Application layout and navigation
- User authentication flow
- Real-time features and WebSocket handling
- State management and data loading
- Theme and language switching
- Responsive design and mobile support

### FRONTEND - src/lib/stores/index.ts
**File Path:** /src/lib/stores/index.ts
**File Type:** Global State Management (TypeScript)
**Total Lines:** ~300 lines
**Purpose:** Centralized Svelte store definitions for application-wide state management

**Key Store Definitions with Line Numbers:**

**User & Authentication Stores:**
- Line 15-25: export const user = writable(null) - Current authenticated user information
- Line 30-40: export const config = writable({}) - Application configuration and feature flags
- Line 45-55: export const settings = writable({}) - User preferences and settings

**Chat & Conversation Stores:**
- Line 60-70: export const chats = writable([]) - List of user's chat conversations
- Line 75-85: export const chatId = writable(null) - Currently active chat ID
- Line 90-100: export const currentChatPage = writable(0) - Pagination for chat list

**AI Model Stores:**
- Line 105-115: export const models = writable([]) - Available AI models list
- Line 120-130: export const selectedModel = writable(null) - Currently selected AI model
- Line 135-145: export const modelConfigs = writable({}) - Model-specific configurations

**UI State Stores:**
- Line 150-160: export const theme = writable('dark') - Current UI theme (light/dark/auto)
- Line 165-175: export const mobile = writable(false) - Mobile device detection
- Line 180-190: export const showSettings = writable(false) - Settings modal visibility
- Line 195-205: export const showSidebar = writable(true) - Sidebar visibility state

**Real-time Communication:**
- Line 210-220: export const socket = writable(null) - WebSocket connection instance
- Line 225-235: export const notifications = writable([]) - Toast notification queue
- Line 240-250: export const typingUsers = writable(new Set()) - Users currently typing

**Application Features:**
- Line 255-265: export const knowledge = writable([]) - Knowledge base collections
- Line 270-280: export const tools = writable([]) - Available external tools
- Line 285-295: export const functions = writable([]) - Custom function definitions

**Derived Stores:**
- Line 300-320: Computed stores that derive from base stores
- isAuthenticated: Derived from user store
- availableModels: Filtered models based on permissions
- unreadNotifications: Count of unread notifications

**Store Utility Functions:**
- Line 325-350: Store initialization and persistence helpers
- Line 355-380: Cross-tab synchronization utilities
- Line 385-410: State reset and cleanup functions

**LocalStorage Integration:**
- Automatic persistence of user preferences
- Theme and language settings
- Chat history caching
- Model selection memory

**Usage in Knowledge Base:** Reference when asking about:
- Application state management
- User authentication state
- Chat and conversation state
- UI state and preferences
- Real-time features and WebSocket state
- Data persistence and caching

### FRONTEND - src/lib/apis/chats.ts
**File Path:** /src/lib/apis/chats.ts
**File Type:** Chat API Client Functions (TypeScript)
**Total Lines:** ~500 lines
**Purpose:** Frontend API client for all chat-related operations with the backend

**Key Functions with Line Numbers:**

**HTTP Client Configuration (Lines 1-30):**
- Line 5-15: Base axios configuration with authentication headers
- Line 20-30: Request/response interceptors for error handling and token refresh

**Chat CRUD Operations:**
- Line 35-55: getChatList(page?: number, limit?: number): Promise<Chat[]> - Fetches paginated user chat list
- Line 60-80: createNewChat(chatData: NewChatRequest): Promise<Chat> - Creates new chat conversation
- Line 85-105: getChatById(chatId: string): Promise<Chat> - Retrieves specific chat with messages
- Line 110-130: updateChatTitle(chatId: string, title: string): Promise<Chat> - Updates chat title
- Line 135-155: deleteChat(chatId: string): Promise<void> - Deletes chat and all messages

**Message Management:**
- Line 160-180: sendMessage(chatId: string, message: MessageRequest): Promise<Message> - Sends new message to chat
- Line 185-205: editMessage(chatId: string, messageId: string, content: string): Promise<Message> - Edits existing message
- Line 210-230: deleteMessage(chatId: string, messageId: string): Promise<void> - Removes specific message
- Line 235-255: getMessageHistory(chatId: string, before?: string): Promise<Message[]> - Loads message history with pagination

**Advanced Chat Features:**
- Line 260-280: searchChats(query: string, filters?: SearchFilters): Promise<SearchResult[]> - Full-text search across chats
- Line 285-305: exportChat(chatId: string, format: 'json' | 'markdown' | 'txt'): Promise<Blob> - Exports chat in various formats
- Line 310-330: importChat(file: File): Promise<Chat> - Imports chat from file
- Line 335-355: shareChat(chatId: string, permissions: SharePermissions): Promise<ShareLink> - Creates shareable chat link

**Chat Organization:**
- Line 360-380: getChatTags(): Promise<string[]> - Retrieves all available chat tags
- Line 385-405: addTagToChat(chatId: string, tag: string): Promise<void> - Adds tag to chat
- Line 410-430: removeTagFromChat(chatId: string, tag: string): Promise<void> - Removes tag from chat
- Line 435-455: archiveChat(chatId: string): Promise<void> - Archives chat without deletion

**Real-time Features:**
- Line 460-480: subscribeToChat(chatId: string, callback: (message: Message) => void): () => void - WebSocket subscription
- Line 485-505: sendTypingIndicator(chatId: string, isTyping: boolean): void - Sends typing status

**Type Definitions:**
```typescript
interface Chat {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  tags: string[];
  archived: boolean;
}

interface Message {
  id: string;
  chat_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}
```

**Error Handling:**
- Network error recovery with retry logic
- Authentication error handling with token refresh
- Validation error display
- Offline mode support with queue

**Usage in Knowledge Base:** Reference when asking about:
- Chat API integration
- Message sending and receiving
- Chat management operations
- Search and organization features
- Real-time chat functionality
- Error handling in chat operations

### FRONTEND - src/lib/components/chat/MessageInput.svelte
**File Path:** /src/lib/components/chat/MessageInput.svelte
**File Type:** Chat Message Input Component (Svelte)
**Total Lines:** ~600 lines
**Purpose:** Advanced chat input component with file upload, formatting, and command support

**Key Functions with Line Numbers:**

**Input Handling:**
- Line 30-50: handleSubmit(event: Event) - Processes message submission with validation
- Line 55-75: handleKeyPress(event: KeyboardEvent) - Keyboard shortcuts and special key handling
- Line 80-100: handleInput(event: InputEvent) - Real-time input processing and validation
- Line 105-125: handlePaste(event: ClipboardEvent) - Clipboard content processing including images

**Text Manipulation:**
- Line 130-150: insertAtCursor(text: string) - Inserts text at current cursor position
- Line 155-175: autoResize() - Automatically resizes textarea based on content
- Line 180-200: formatText(format: 'bold' | 'italic' | 'code') - Applies markdown formatting
- Line 205-225: insertEmoji(emoji: string) - Inserts emoji at cursor position

**File Upload Features:**
- Line 230-250: handleFileUpload(files: FileList) - Processes file uploads with validation
- Line 255-275: handleDragDrop(event: DragEvent) - Drag and drop file handling
- Line 280-300: validateFileType(file: File): boolean - Validates file types and sizes
- Line 305-325: generateFilePreview(file: File) - Creates preview for uploaded files

**Advanced Input Features:**
- Line 330-350: handleMention(query: string) - User mention autocomplete functionality
- Line 355-375: handleSlashCommand(command: string) - Slash command processing and suggestions
- Line 380-400: handleVoiceInput() - Voice-to-text input processing
- Line 405-425: toggleEmojiPicker() - Emoji picker visibility management

**Message Processing:**
- Line 430-450: validateMessage(content: string): ValidationResult - Input validation before sending
- Line 455-475: processMarkdown(text: string): string - Markdown processing and preview
- Line 480-500: sanitizeInput(text: string): string - Input sanitization for security
- Line 505-525: generateMessageMetadata(): MessageMetadata - Creates message metadata

**UI State Management:**
- Line 530-550: updateCharacterCount() - Updates character count display
- Line 555-575: showTypingIndicator() - Manages typing indicator state
- Line 580-600: handleFocusState() - Focus and blur event handling

**Component Props:**
```typescript
export let chatId: string;
export let disabled: boolean = false;
export let placeholder: string = 'Type a message...';
export let maxLength: number = 4000;
export let allowFileUpload: boolean = true;
export let allowVoiceInput: boolean = true;
```

**Features Supported:**
- Multi-line text input with auto-resize
- File drag-and-drop upload
- Image paste from clipboard
- Emoji picker integration
- User mention autocomplete
- Slash command suggestions
- Voice input (speech-to-text)
- Markdown formatting shortcuts
- Character count and limits
- Typing indicators

**Keyboard Shortcuts:**
- Ctrl/Cmd + Enter: Send message
- Ctrl/Cmd + B: Bold formatting
- Ctrl/Cmd + I: Italic formatting
- Ctrl/Cmd + K: Code formatting
- Tab: Accept autocomplete suggestion
- Escape: Cancel current operation

**Usage in Knowledge Base:** Reference when asking about:
- Chat input functionality
- File upload in chats
- Message formatting and markdown
- Voice input features
- Emoji and mention support
- Keyboard shortcuts
- Input validation and security

This comprehensive inventory now includes detailed function-level analysis with line numbers, specific explanations of what each file contains, and how to reference them in your knowledge base. Each entry provides the exact information needed to understand and work with the codebase effectively.
