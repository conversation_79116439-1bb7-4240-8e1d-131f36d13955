name: Feature Request
description: Suggest an idea for this project
title: 'feat: '
labels: ['triage']
body:
  - type: markdown
    attributes:
      value: |
        ## Important Notes
        ### Before submitting
        Please check the [Issues](https://github.com/open-webui/open-webui/issues) or [Discussions](https://github.com/open-webui/open-webui/discussions) to see if a similar request has been posted.
        It's likely we're already tracking it! If you’re unsure, start a discussion post first.
        This will help us efficiently focus on improving the project.

        ### Collaborate respectfully
        We value a **constructive attitude**, so please be mindful of your communication. If negativity is part of your approach, our capacity to engage may be limited. We're here to help if you're **open to learning** and **communicating positively**.

        Remember:
        - Open WebUI is a **volunteer-driven project**
        - It's managed by a **single maintainer**
        - It's supported by contributors who also have **full-time jobs**

        We appreciate your time and ask that you **respect ours**.


        ### Contributing
        If you encounter an issue, we highly encourage you to submit a pull request or fork the project. We actively work to prevent contributor burnout to maintain the quality and continuity of Open WebUI.

        ### Bug reproducibility
        If a bug cannot be reproduced with a `:main` or `:dev` Docker setup, or a `pip install` with Python 3.11, it may require additional help from the community. In such cases, we will move it to the "[issues](https://github.com/open-webui/open-webui/discussions/categories/issues)" Discussions section due to our limited resources. We encourage the community to assist with these issues. Remember, it’s not that the issue doesn’t exist; we need your help!

  - type: checkboxes
    id: existing-issue
    attributes:
      label: Check Existing Issues
      description: Please confirm that you've checked for existing similar requests
      options:
        - label: I have searched the existing issues and discussions.
          required: true
  - type: textarea
    id: problem-description
    attributes:
      label: Problem Description
      description: Is your feature request related to a problem? Please provide a clear and concise description of what the problem is.
      placeholder: "Ex. I'm always frustrated when..."
    validations:
      required: true
  - type: textarea
    id: solution-description
    attributes:
      label: Desired Solution you'd like
      description: Clearly describe what you want to happen.
    validations:
      required: true
  - type: textarea
    id: alternatives-considered
    attributes:
      label: Alternatives Considered
      description: A clear and concise description of any alternative solutions or features you've considered.
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context or screenshots about the feature request here.
