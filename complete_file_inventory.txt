# Open WebUI - Complete File Inventory
Generated on: 2025-06-23
Project: Open WebUI - Web-based AI Model Interface

## File Type Legend:
🔧 Configuration | 📚 Documentation | 🐍 Python | 🌐 Frontend | 🐳 Docker | 📦 Package | 🎨 Asset | 🧪 Test | 📄 Script

---

## ROOT LEVEL FILES

| File Name | Type | Explanation |
|-----------|------|-------------|
| .env.example | 🔧 | Template environment variables file showing all configurable options for the application |
| .gitignore | 🔧 | Git ignore file specifying which files/folders should not be tracked in version control |
| CHANGELOG.md | 📚 | Detailed log of all changes, updates, and version releases for the project |
| CODE_OF_CONDUCT.md | 📚 | Community guidelines and behavioral expectations for contributors |
| CONTRIBUTOR_LICENSE_AGREEMENT | 📚 | Legal agreement outlining terms for code contributions to the project |
| Dockerfile | 🐳 | Main Docker container definition for building the application image |
| INSTALLATION.md | 📚 | Step-by-step installation instructions for various deployment methods |
| LICENSE | 📚 | MIT license file defining usage rights and restrictions |
| Makefile | 🔧 | Build automation file with commands for development and deployment tasks |
| README.md | 📚 | Main project documentation with overview, features, and quick start guide |
| TROUBLESHOOTING.md | 📚 | Common issues and their solutions for users and developers |
| cypress.config.ts | 🔧 | Configuration for Cypress end-to-end testing framework |
| demo.gif | 🎨 | Animated demonstration of the application's user interface |
| docker-compose.yaml | 🐳 | Main Docker Compose file for multi-container deployment |
| docker-compose.a1111-test.yaml | 🐳 | Docker Compose for testing with Automatic1111 integration |
| docker-compose.amdgpu.yaml | 🐳 | Docker Compose optimized for AMD GPU acceleration |
| docker-compose.api.yaml | 🐳 | Docker Compose for API-only deployment without frontend |
| docker-compose.data.yaml | 🐳 | Docker Compose with persistent data volume configurations |
| docker-compose.gpu.yaml | 🐳 | Docker Compose with NVIDIA GPU support for AI model acceleration |
| docker-compose.playwright.yaml | 🐳 | Docker Compose for Playwright browser automation testing |
| hatch_build.py | 🔧 | Python build script for packaging and distribution using Hatch |
| i18next-parser.config.ts | 🔧 | Configuration for extracting translatable strings for internationalization |
| package.json | 📦 | Node.js package definition with dependencies and build scripts |
| package-lock.json | 📦 | Locked versions of Node.js dependencies for reproducible builds |
| postcss.config.js | 🔧 | PostCSS configuration for CSS processing and optimization |
| pyproject.toml | 📦 | Python project configuration with dependencies and build settings |
| svelte.config.js | 🔧 | SvelteKit framework configuration for the frontend application |
| tailwind.config.js | 🔧 | Tailwind CSS configuration for utility-first styling |
| tsconfig.json | 🔧 | TypeScript compiler configuration for type checking and compilation |
| uv.lock | 📦 | UV package manager lock file for Python dependencies |
| vite.config.ts | 🔧 | Vite build tool configuration for fast development and production builds |
| confirm_remove.sh | 📄 | Shell script for safely removing containers with user confirmation |
| contribution_stats.py | 📄 | Python script to generate contributor statistics from git history |
| run-compose.sh | 📄 | Shell script to run Docker Compose with proper configurations |
| run-ollama-docker.sh | 📄 | Shell script to run Ollama AI model server in Docker |
| run.sh | 📄 | Main shell script to start the application in development mode |
| update_ollama_models.sh | 📄 | Shell script to update and manage Ollama AI models |

---

## BACKEND DIRECTORY (/backend)

| File Name | Type | Explanation |
|-----------|------|-------------|
| requirements.txt | 📦 | Python package dependencies for the backend FastAPI application |
| dev.sh | 📄 | Development startup script with hot reload and debugging enabled |
| start.sh | 📄 | Production startup script for the FastAPI backend server |
| start_windows.bat | 📄 | Windows batch file to start the backend on Windows systems |

### Backend Data Directory (/backend/data)
| File Name | Type | Explanation |
|-----------|------|-------------|
| readme.txt | 📚 | Explanation that this directory stores backend files (database, documents, etc.) |

### Backend Main Application (/backend/open_webui)
| File Name | Type | Explanation |
|-----------|------|-------------|
| main.py | 🐍 | Main FastAPI application entry point with all route configurations and middleware |
| config.py | 🐍 | Configuration management system with persistent settings and environment variables |
| env.py | 🐍 | Environment variable definitions and validation for application settings |
| constants.py | 🐍 | Application-wide constants including error messages and default values |

### Backend Apps Directory (/backend/open_webui/apps)
Contains modular application components for different AI integrations and features.

### Backend Models Directory (/backend/open_webui/models)
Contains SQLAlchemy database models for all data entities (users, chats, files, etc.).

### Backend Routers Directory (/backend/open_webui/routers)
Contains FastAPI route handlers for all API endpoints:
- auths.py: Authentication and authorization endpoints
- chats.py: Chat management and conversation handling
- files.py: File upload, storage, and retrieval
- models.py: AI model management and configuration
- users.py: User account management
- And many more specialized routers

### Backend Utils Directory (/backend/open_webui/utils)
Contains utility functions for various operations like logging, validation, and helper functions.

---

## FRONTEND DIRECTORY (/src)

| File Name | Type | Explanation |
|-----------|------|-------------|
| app.css | 🌐 | Global CSS styles and custom styling for the application |
| app.d.ts | 🌐 | TypeScript type definitions for the SvelteKit application |
| app.html | 🌐 | Main HTML template that wraps the entire SvelteKit application |
| tailwind.css | 🌐 | Tailwind CSS imports and custom utility classes |

### Frontend Library Directory (/src/lib)
Contains reusable Svelte components, utilities, and application logic:
- components/: UI components like buttons, modals, chat interface
- stores/: Svelte stores for state management
- utils/: Frontend utility functions
- i18n/: Internationalization files for multiple languages
- apis/: API client functions for backend communication

### Frontend Routes Directory (/src/routes)
Contains SvelteKit routes and pages:
- +layout.svelte: Main application layout wrapper
- +page.svelte: Home page component
- (app)/: Protected application routes
- Various route folders for different pages

---

## STATIC ASSETS DIRECTORY (/static)

### Static Assets (/static/assets)
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| emojis/ | 🎨 | Over 4000 SVG emoji files for chat and UI enhancement |
| fonts/ | 🎨 | Web fonts including Inter, Archivo, and other typefaces |
| images/ | 🎨 | Application images including backgrounds and UI graphics |

### Static Audio (/static/audio)
| File Name | Type | Explanation |
|-----------|------|-------------|
| greeting.mp3 | 🎨 | Audio file for greeting notifications |
| notification.mp3 | 🎨 | Audio file for system notifications |

### Static Root Files (/static)
| File Name | Type | Explanation |
|-----------|------|-------------|
| doge.png | 🎨 | Doge meme image used in the application |
| favicon.png | 🎨 | Browser favicon for the application |
| manifest.json | 🔧 | Progressive Web App manifest for mobile installation |
| opensearch.xml | 🔧 | OpenSearch description for browser search integration |
| robots.txt | 🔧 | Search engine crawler instructions |
| user.png | 🎨 | Default user avatar image |

### Static Themes (/static/themes)
| File Name | Type | Explanation |
|-----------|------|-------------|
| rosepine.css | 🎨 | Rose Pine dark theme CSS (currently disabled) |
| rosepine-dawn.css | 🎨 | Rose Pine light theme CSS (currently disabled) |

---

## DOCUMENTATION DIRECTORY (/docs)

| File Name | Type | Explanation |
|-----------|------|-------------|
| CONTRIBUTING.md | 📚 | Guidelines for contributing code, reporting issues, and development setup |
| README.md | 📚 | Documentation overview and links to other documentation |
| SECURITY.md | 📚 | Security policy and vulnerability reporting procedures |
| apache.md | 📚 | Instructions for deploying with Apache web server |

---

## TESTING DIRECTORY (/cypress)

| File Name | Type | Explanation |
|-----------|------|-------------|
| tsconfig.json | 🔧 | TypeScript configuration specific to Cypress tests |

### Cypress Subdirectories
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| data/ | 🧪 | Test data files and fixtures for end-to-end tests |
| e2e/ | 🧪 | End-to-end test specifications and scenarios |
| support/ | 🧪 | Cypress support files and custom commands |

---

## KUBERNETES DIRECTORY (/kubernetes)

### Kubernetes Subdirectories
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| helm/ | 🔧 | Helm charts for Kubernetes deployment and management |
| manifest/ | 🔧 | Raw Kubernetes YAML manifests for deployment |

---

## SCRIPTS DIRECTORY (/scripts)

| File Name | Type | Explanation |
|-----------|------|-------------|
| prepare-pyodide.js | 📄 | JavaScript script to prepare Pyodide for Python-in-browser functionality |

---

## TEST DIRECTORY (/test)

### Test Files Subdirectory (/test/test_files)
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| image_gen/ | 🧪 | Test files for image generation functionality |

### Image Generation Test Files (/test/test_files/image_gen)
| File Name | Type | Explanation |
|-----------|------|-------------|
| sd-empty.pt | 🧪 | Empty PyTorch model file for Stable Diffusion testing |

---

## SUMMARY

**Total Files**: Approximately 5,000+ files
**Main Technologies**: SvelteKit, FastAPI, Docker, TypeScript, Python
**Architecture**: Full-stack web application with AI model integration
**Purpose**: Web-based interface for interacting with various AI models (Ollama, OpenAI, etc.)

---

## DETAILED BACKEND COMPONENTS

### Backend Apps Directory (/backend/open_webui/apps)
| File Name | Type | Explanation |
|-----------|------|-------------|
| audio/ | 🐍 | Audio processing and speech-to-text/text-to-speech functionality |
| images/ | 🐍 | Image generation and processing using various AI models |
| ollama/ | 🐍 | Integration with Ollama local AI model server |
| openai/ | 🐍 | OpenAI API integration for GPT models and services |
| retrieval/ | 🐍 | RAG (Retrieval Augmented Generation) and document processing |
| webui/ | 🐍 | Core WebUI functionality and user interface logic |

### Backend Models Directory (/backend/open_webui/models)
| File Name | Type | Explanation |
|-----------|------|-------------|
| auths.py | 🐍 | Authentication and session management database models |
| chats.py | 🐍 | Chat conversation and message storage models |
| channels.py | 🐍 | Communication channels and group chat models |
| configs.py | 🐍 | Application configuration storage models |
| evaluations.py | 🐍 | AI model evaluation and rating system models |
| files.py | 🐍 | File upload, storage, and metadata models |
| folders.py | 🐍 | File organization and folder structure models |
| functions.py | 🐍 | Custom function definitions and execution models |
| groups.py | 🐍 | User groups and permission management models |
| knowledge.py | 🐍 | Knowledge base and document collection models |
| memories.py | 🐍 | Conversation memory and context storage models |
| models.py | 🐍 | AI model definitions and configuration models |
| notes.py | 🐍 | User notes and annotation system models |
| prompts.py | 🐍 | Prompt templates and management models |
| tools.py | 🐍 | External tool integration and execution models |
| users.py | 🐍 | User account and profile management models |
| utils.py | 🐍 | Utility database models and helper functions |

### Backend Routers Directory (/backend/open_webui/routers)
| File Name | Type | Explanation |
|-----------|------|-------------|
| audio.py | 🐍 | API endpoints for audio processing and voice features |
| auths.py | 🐍 | Authentication, login, logout, and session management endpoints |
| channels.py | 🐍 | Channel creation, management, and communication endpoints |
| chats.py | 🐍 | Chat conversation CRUD operations and message handling |
| configs.py | 🐍 | Application configuration management endpoints |
| evaluations.py | 🐍 | Model evaluation and feedback collection endpoints |
| files.py | 🐍 | File upload, download, and management endpoints |
| folders.py | 🐍 | Folder organization and file structure endpoints |
| functions.py | 🐍 | Custom function definition and execution endpoints |
| groups.py | 🐍 | User group management and permission endpoints |
| images.py | 🐍 | Image generation and processing endpoints |
| knowledge.py | 🐍 | Knowledge base management and search endpoints |
| memories.py | 🐍 | Conversation memory and context management endpoints |
| models.py | 🐍 | AI model configuration and management endpoints |
| notes.py | 🐍 | Note creation, editing, and organization endpoints |
| ollama.py | 🐍 | Ollama model server integration and proxy endpoints |
| openai.py | 🐍 | OpenAI API integration and proxy endpoints |
| pipelines.py | 🐍 | AI processing pipeline management endpoints |
| prompts.py | 🐍 | Prompt template management and sharing endpoints |
| retrieval.py | 🐍 | Document retrieval and RAG functionality endpoints |
| tasks.py | 🐍 | Background task management and monitoring endpoints |
| tools.py | 🐍 | External tool integration and execution endpoints |
| users.py | 🐍 | User account management and profile endpoints |
| utils.py | 🐍 | Utility endpoints for various helper functions |

### Backend Utils Directory (/backend/open_webui/utils)
| File Name | Type | Explanation |
|-----------|------|-------------|
| audit.py | 🐍 | Audit logging and security monitoring utilities |
| auth.py | 🐍 | Authentication helper functions and token management |
| logger.py | 🐍 | Logging configuration and utility functions |
| models.py | 🐍 | Model management and validation utilities |
| plugin.py | 🐍 | Plugin system for extending application functionality |
| task.py | 🐍 | Background task execution and management utilities |
| webhook.py | 🐍 | Webhook handling and external service integration |

---

## DETAILED FRONTEND COMPONENTS

### Frontend Components (/src/lib/components)
| Subdirectory | Type | Explanation |
|--------------|------|-------------|
| admin/ | 🌐 | Administrative interface components for system management |
| chat/ | 🌐 | Chat interface components including message bubbles and input |
| common/ | 🌐 | Reusable UI components like buttons, modals, and forms |
| icons/ | 🌐 | SVG icon components for consistent iconography |
| layout/ | 🌐 | Layout components for page structure and navigation |
| workspace/ | 🌐 | Workspace and project management interface components |

### Frontend Stores (/src/lib/stores)
| File Name | Type | Explanation |
|-----------|------|-------------|
| index.ts | 🌐 | Main store exports and global state management |
| Various stores | 🌐 | Individual stores for user, settings, chats, models, etc. |

### Frontend APIs (/src/lib/apis)
| File Name | Type | Explanation |
|-----------|------|-------------|
| auths.ts | 🌐 | Authentication API client functions |
| chats.ts | 🌐 | Chat management API client functions |
| configs.ts | 🌐 | Configuration API client functions |
| files.ts | 🌐 | File management API client functions |
| functions.ts | 🌐 | Custom functions API client functions |
| images.ts | 🌐 | Image generation API client functions |
| knowledge.ts | 🌐 | Knowledge base API client functions |
| models.ts | 🌐 | Model management API client functions |
| ollama.ts | 🌐 | Ollama integration API client functions |
| openai.ts | 🌐 | OpenAI integration API client functions |
| prompts.ts | 🌐 | Prompt management API client functions |
| tools.ts | 🌐 | Tools integration API client functions |
| users.ts | 🌐 | User management API client functions |
| utils.ts | 🌐 | Utility API client functions |

### Frontend Routes (/src/routes)
| Directory | Type | Explanation |
|-----------|------|-------------|
| (app)/ | 🌐 | Protected application routes requiring authentication |
| auth/ | 🌐 | Authentication pages (login, signup, password reset) |
| admin/ | 🌐 | Administrative interface routes |
| workspace/ | 🌐 | Workspace and project management routes |

---

## STATIC ASSETS DETAILED

### Static Assets Fonts (/static/assets/fonts)
| File Name | Type | Explanation |
|-----------|------|-------------|
| Archivo-Variable.ttf | 🎨 | Variable font for headings and display text |
| InstrumentSerif-Italic.ttf | 🎨 | Serif italic font for emphasis and quotes |
| InstrumentSerif-Regular.ttf | 🎨 | Serif regular font for body text |
| Inter-Variable.ttf | 🎨 | Variable sans-serif font for UI elements |
| Mona-Sans.woff2 | 🎨 | GitHub's Mona Sans font for code and technical text |
| Vazirmatn-Variable.ttf | 🎨 | Persian/Farsi font for internationalization |

### Static Assets Images (/static/assets/images)
| File Name | Type | Explanation |
|-----------|------|-------------|
| adam.jpg | 🎨 | Profile image or avatar option |
| earth.jpg | 🎨 | Earth background image for themes |
| galaxy.jpg | 🎨 | Galaxy background image for themes |
| space.jpg | 🎨 | Space background image for themes |

### Static Pyodide (/static/pyodide)
| File Name | Type | Explanation |
|-----------|------|-------------|
| pyodide-lock.json | 🔧 | Lock file for Pyodide Python packages in browser |

### Static Additional Files (/static/static)
| File Name | Type | Explanation |
|-----------|------|-------------|
| apple-touch-icon.png | 🎨 | Apple device home screen icon |
| custom.css | 🎨 | Custom CSS overrides and additional styles |
| favicon.ico | 🎨 | Browser favicon in ICO format |
| favicon.png | 🎨 | Browser favicon in PNG format |
| favicon.svg | 🎨 | Browser favicon in SVG format |
| favicon-96x96.png | 🎨 | High-resolution favicon for modern browsers |
| favicon-dark.png | 🎨 | Dark mode favicon variant |
| loader.js | 🌐 | JavaScript loading and initialization script |
| site.webmanifest | 🔧 | Web app manifest for PWA functionality |
| splash.png | 🎨 | Splash screen image for app loading |
| splash-dark.png | 🎨 | Dark mode splash screen image |
| web-app-manifest-192x192.png | 🎨 | PWA icon 192x192 pixels |
| web-app-manifest-512x512.png | 🎨 | PWA icon 512x512 pixels |

This comprehensive inventory covers all major files and directories in the Open WebUI project, providing detailed explanations for each component's purpose and functionality. The project represents a sophisticated full-stack web application for AI model interaction with extensive customization and integration capabilities.
